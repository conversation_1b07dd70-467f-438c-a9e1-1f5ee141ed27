/* 飞书分享插件样式 */

/* 设置页面样式 */
.feishu-setting-description {
    margin-bottom: 20px;
}

.feishu-callback-input {
    width: 100% !important;
    height: 80px !important;
}

/* 文件夹选择器样式 */
.folder-list-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
    margin: 10px 0;
}

.button-container {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    gap: 10px;
}

.folder-breadcrumb {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding: 8px 12px;
    background: var(--background-secondary);
    border-radius: 6px;
    font-size: 14px;
}

.breadcrumb-item {
    cursor: pointer;
    color: var(--text-accent);
    text-decoration: none;
    padding: 2px 4px;
    border-radius: 3px;
}

.breadcrumb-item:hover {
    background: var(--background-modifier-hover);
}

.breadcrumb-item.current {
    font-weight: bold;
    color: var(--text-normal);
    cursor: default;
}

.breadcrumb-separator {
    margin: 0 8px;
    color: var(--text-muted);
}

/* 加载和错误状态 */
.loading-indicator {
    text-align: center;
    padding: 20px;
    color: var(--text-muted);
    font-style: italic;
}

.error-message {
    text-align: center;
    padding: 20px;
    color: var(--text-error);
    background: var(--background-modifier-error);
    border-radius: 6px;
    margin: 10px 0;
}

.empty-message {
    text-align: center;
    padding: 20px;
    color: var(--text-muted);
    font-style: italic;
}

/* 文件夹项目样式 */
.folder-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid var(--background-modifier-border);
    transition: background-color 0.2s ease;
}

.folder-item:hover {
    background-color: var(--background-modifier-hover);
}

.folder-item:last-child {
    border-bottom: none;
}

.folder-icon {
    margin-right: 12px;
    font-size: 16px;
    color: var(--text-accent);
}

.folder-name {
    flex: 1;
    font-size: 14px;
    color: var(--text-normal);
}

/* 成功通知样式 */
.feishu-success-container {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 16px;
    background: linear-gradient(135deg, var(--background-primary), var(--background-secondary));
    border: 2px solid var(--interactive-accent);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    min-width: 320px;
    max-width: 400px;
}

.feishu-success-header {
    display: flex;
    align-items: center;
    gap: 12px;
}

.feishu-icon-container {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--interactive-accent), var(--interactive-accent-hover));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.feishu-success-icon {
    font-size: 24px;
    font-weight: bold;
    color: white;
}

.feishu-header-text {
    flex: 1;
}

.feishu-success-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-normal);
    margin: 0 0 4px 0;
}

.feishu-success-subtitle {
    font-size: 14px;
    color: var(--text-muted);
    margin: 0;
}

.feishu-button-group {
    display: flex;
    gap: 12px;
    margin-top: 8px;
}

.feishu-copy-btn {
    flex: 1;
    padding: 12px 16px;
    background: linear-gradient(135deg, var(--interactive-accent), var(--interactive-accent-hover));
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.feishu-copy-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.feishu-copy-btn.success {
    background: linear-gradient(135deg, #4CAF50, #45a049);
}

.feishu-copy-btn.error {
    background: linear-gradient(135deg, #f44336, #d32f2f);
}

.feishu-open-btn {
    padding: 12px 16px;
    background: var(--background-secondary);
    color: var(--text-normal);
    border: 2px solid var(--background-modifier-border);
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.feishu-open-btn:hover {
    background: var(--background-modifier-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.feishu-close-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    background: transparent;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: var(--text-muted);
    transition: all 0.2s ease;
}

.feishu-close-btn:hover {
    background: var(--background-modifier-hover);
    color: var(--text-normal);
}

/* 授权状态样式 */
.auth-status-success {
    color: var(--text-success);
}

.auth-status-error {
    color: var(--text-error);
}
