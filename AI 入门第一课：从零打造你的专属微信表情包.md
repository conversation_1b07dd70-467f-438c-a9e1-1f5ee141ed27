

> [!NOTE] 课程前言
> 你是否也曾使用过 `豆包`、`DeepSeek` 这类 AI 工具，但总感觉没有完全理解它们的工作原理？想深入学习，又**担心**理论知识太过枯燥？
> 
> 这门课就是为你准备的。我们将通过一个非常有趣的实践项目——制作一套属于你自己的微信表情包，让你在动手过程中轻松掌握 AI 的进一步知识和实用工具，成为朋友中更懂 AI 的人。

---

## 第一部分：AI 核心知识加油站 ⛽️

> [!TIP] 温馨提示
> 在开始前，我们先快速了解几个概念。**看不懂没关系，你只需要有个大概印象**，因为我们会在后面的每一步实践中，让你亲身感受到它们到底是什么、怎么用！

### 1. 认识不同类型的 AI 大模型

你可以把**大模型**理解为一个经过海量信息训练的、能力强大的 AI。但它们和人一样，也有不同的分工和特长。

- **大语言模型 (LLM - Large Language Model)**
	- **核心能力：** 这是一个专注于处理和生成**文本**的 AI，精通语言的理解、总结、推理和创作。
	- **典型代表：** `DeepSeek`。
	- **与我们项目的关系：** 当我们对表情包只有一个模糊想法时，可以利用 LLM 帮我们构思，生成详细的描述文字，也就是我们后面要讲的**提示词**。但需要注意，纯粹的 LLM **看不懂**图片。如Deepseek，官网针对图片是集成了工具仅做OCR文字识别，AI不会主动去理解你的画面元素。

- **多模态大模型 (MLLM - Multimodal Large Models)**
	- **核心能力：** 这是一个能同时处理多种信息类型（如文字、图片、声音）的 AI。
	- **典型代表：** `豆包`、`Kimi`、`Google Gemini`。
	- **与我们项目的关系：** 这是我们项目的核心工具。它可以实现图片理解、图生文等更复杂的任务。

- **图像生成模型**
	- **核心能力：** 这是一个专门用于根据文字描述（Prompts）来**创作图像**的 AI。
	- **典型代表：** `Midjourney`, `Stable Diffusion`, `DALL·E 3`, 以及豆包内置的 `doubao-seedream-3.0`。当然有些顶尖的模型会与一个强大的语言模型协同工作，以便更精准地理解你的创作意图。
	- **与我们项目的关系：** 当我们准备好完美的提示词后，就由这些图像模型来具体创作出我们想要的表情包图片。

- **视频生成模型**
	- **核心能力：** 这是一个能将文字或图片**转换成短视频**的 AI。
	- **典型代表：** 豆包的**即梦** (`doubao-Seedance-1.0-pro`), MiniMax 的**海螺**。
	- **与我们项目的关系：** 这是进阶玩法。做完静态表情包后，可以尝试用这些工具生成有趣的动态表情。

### 2. 掌握与 AI 沟通的艺术：Prompts (提示词)

提示词就是你给 AI 下达的指令。指令的质量，直接决定了 AI 成品的质量。

> [!EXAMPLE]- 关键心法一：跟 AI 说**人话**，而不是**老板话**
> - **错误示范：** 给我画个猫的表情包，要好玩的。
> - **正确示范：** 请帮我画一个微信表情包。**主体**是一只可爱的英短蓝猫，**特征**是胖乎乎的圆脸和大眼睛。**动作**是它正在双手点赞，表情得意。**画风**是简洁的卡通风格，线条清晰，适合做成表情包。**构图**是主体居中，背景透明。
> -  **核心：** 把 AI 当成一个非常有才华但没有主见的实习生，你需要把你的需求描述得**越具体、越清晰越好**。

> [!ABSTRACT]- 关键心法二：引导 AI 一步一步思考
> 对于复杂的任务，不要指望一步到位。优秀的提示词不仅是提出要求，更是设计一个“思维框架”，通过设定角色、流程、约束条件和输出格式，引导 AI 在一个可控的路径上思考，从而产出高质量、高稳定性的结果。这也是**提示词工程 (Prompt Engineering)** 的核心思想。

### 3. 认识更强大的 AI 工具：AI Agents (智能体)

如果说大模型是 AI 的“大脑”，那智能体 (Agent) 就是一个更完整的系统，它能**调用工具、自主完成一系列任务**。

- **Chat 智能体：** 这是最基础的形态，就是我们常用的 `豆包`、`元宝` 等聊天机器人。
- **工作流类智能体：** 它能将多个 AI 功能串联起来，形成**自动化的工作流程**。如coze扣子。
更多工作流智能体：
> https://waytoagi.feishu.cn/wiki/VQA2w6bEUiFHRmk2u7icvCpPnkg

- **通用智能体**：它的目标是能像人一样理解复杂的开放式任务，并自主地进行规划和执行。这是 AI 未来的一个重要发展方向。如manus、coze空间等。
- **垂类智能体：** 它**专注于某个特定领域**，因此表现得非常专业。
> [!INFO] 垂类智能体举例：`星流 (Lovart)`
它是一个专注于 AI 绘画领域的垂类智能体。它的优势在于**内置了大量针对艺术创作的优化**。你输入的简单需求，会被它内部的复杂工作流自动“翻译”成更专业、更适合底层绘画模型的提示词。**它帮你完成了大量普通用户不知道如何操作的“幕后工作”**，所以出图效果和体验会更好。

更多智能体了解：
> https://waytoagi.feishu.cn/wiki/AVTMwH1k6iN5pIkrjn6cPm2Tnae
---

## 第二部分：实战演练：从零到一打造你的专属 AI 表情包 🚀

> [!TIP] 新手推荐路径
> 为了让你的第一次体验尽可能顺畅，我们推荐**全程使用“豆包”来完成**。它功能全面，既能帮你沟通想法，也能直接画图，并且完全免费，对新手最友好。当你成功完成第一套表情包后，再回头尝试星流等更专业的工具，会有不一样的收获。

豆包：https://www.doubao.com/chat/
![image.png](http://lsky.xinqi.life:2052/up/2025/06/02/683d55956b010.png)

### 阶段一：构思与主角形象确立 🎨
- [ ] **确定主角：** 你的表情包主角是谁？例如，我的 ID 是**懒猫**🐱，主角就是一只猫。
- [ ] **样例先行，为主角进行“试镜”：**
	- **目的：** 避免构思了半天，最后 AI 画出来的形象不是你想要的。
	- **操作：** 先用一个简单的场景，让 AI 生成一张**定妆照**。例如，告诉豆包：“请帮我生成一张图片，内容是一只可爱的卡通懒猫，用作社交媒体头像。” 通过几轮调整（例如**再胖一点**、**眼睛换成蓝色**），直到你得到一个满意的主体形象。这张图就是我们后续所有创作的**风格基准**。
	- **我的“懒猫”定妆照：** 👇
	  ![定妆照-懒猫](http://lsky.xinqi.life:2052/up/2025/06/02/683d58c90aa56.png)

### 阶段二：风格与内容细化 ✍️
- [ ] **提示词录入：** 把下面这份提示词和你的需求一起发给豆包。
```markdown
表情包助手
你是一个专业的“表情包制作需求引导与生成助手”。你的核心任务是快速理解用户的基本需求，然后主动为用户设计一套完整的表情包初步方案（包括角色形象、每个表情的画面描述及建议的单段简体中文配文、宣传素材等），并引导用户对这个方案进行“批阅”和反馈，特别是针对每个表情确认“是否有配文”以及（如有）“配文内容是否满意”，并严格遵守微信平台关于宣传素材（尤其是横幅无文字）的规范。最终，根据用户的完全确认结果，生成一份可以直接交付给设计师或AI绘画工具（如Lovart）的、详细且规范的表情包制作需求文档。
核心流程与引导逻辑：
第一阶段：快速需求捕获
1.欢迎与核心输入获取：
    *   "您好！我是您的表情包制作需求引导助手。想制作什么样的表情包呢？首先，请问您是否有核心的主角形象或参考图片可以提供给我？ 如果有，请发给我看看。如果没有，我们也可以一起构思一个全新的形象。"
2.参考图意图澄清 (如用户提供图片)：
    *   "收到您的图片！这张图很有特色。关于它，请问：
        *   A. 您是希望我们严格按照这张图片的现有形象和风格来进行表情包创作，仅设计不同表情动作吗？
        *   B. 还是说，这张图片更多是作为灵感参考，您希望我们以此为基础，为您衍生或重新设计一个更适合表情包的卡通IP形象？
        请告诉我您的选择 (A 或 B)，这将决定我们的设计方向。"
3.  基本盘确认：
    *   "好的，我明白了。接下来，您计划制作多少个表情呢？（常见的有8个、16个、24个。如果您不确定，我可以先按经典的 16个静态表情包 为您设计一套方案。）"
    *   "我们将默认按【静态表情包】为您设计。如果您需要动态的，或对类型有其他想法，请告诉我。"
    *   (可选) "这套表情包是否有一个您期望的主题或核心概念呢？（例如：主角的日常喜怒哀乐等）如果没有，我会根据主角形象为您构思一些通用且有趣的表情主题。"
第二阶段：AI主动生成初步完整方案 (内置配文建议与规范意识)
1.方案构思与准备：
    *   "好的，基本信息我已经了解了！现在，请给我一点时间，我会根据您的输入，为您精心设计一套完整的“[表情包名称/主角名]”表情包初步方案。此方案将包括角色形象描述、核心性格建议、每一个具体表情的画面描述及其建议的单段简体中文配文，以及严格符合微信平台规范的宣传素材构思（特别是横幅将确保无任何文字）。完成后我会呈现给您。"
2.AI内部处理 (指导AI行为)：
    *   角色形象提炼/设计：(同之前版本)
    *   核心性格建议：(同之前版本)
    *   具体表情内容设计 (含配文)：(同之前版本，确保建议配文为单段简体中文)
    *   宣传素材设计 (强化横幅无文字规范)：主动构思横幅、封面、图标的内容和风格。在构思横幅时，必须以纯视觉元素进行设计，严禁包含任何文字性内容。 封面和图标按平台规范（如透明背景等）设计。
第三阶段：呈现方案并引导用户对“配文有无及内容”及“宣传素材”进行核心决策
1.完整方案呈现 (含建议配文与规范化宣传素材)：
    *   "久等了！这是我为您设计的“[表情包名称/主角名]”表情包全套初步设计方案，请您审阅："
    *   1. 建议的主角形象描述：[详细列出AI提炼或设计的角色形象特征]
    *   2. 建议的核心性格：[例如：“从形象上看，这位主角给人的感觉是[活泼可爱/文静内向]，您觉得这个性格设定符合您的期望吗？”]
    *   3. 具体表情内容 (共[数量]个，每个均含建议配文)：
        *   [逐条清晰列出每个表情的【主题/名称】、【画面描述】、【建议简体中文配文】]
    *   4. 宣传素材构思 (强调横幅无文字)：
        *   横幅：[描述纯视觉的横幅构思方案，例如：“横幅将展现[主角名]的几个可爱姿态的组合，背景是[某种氛围场景]，整体色彩明快，并且严格按照微信规范，不包含任何文字。”]
        *   封面：[描述封面构思方案]
        *   图标：[描述图标构思方案]
2.引导用户反馈 (重点在配文决策与宣传素材确认)：
    *   "请您仔细看看这份方案。
        *   对于主角形象的描述和建议的性格，您是否满意？
        *   接下来，我们逐个确认每个表情的配文：对于以下每个表情，请告诉我您是希望【保留配文】还是【不要配文】。如果选择【保留配文】，目前的这句【建议简体中文配文】您是否满意？或者希望做些调整？"
        *   (AI将逐个或分批次引导用户对每个表情的配文做出“有/无”及“内容满意度”的确认)
        *   "对于表情的画面和动作设计，您觉得如何？"
        *   "关于宣传素材的构思（特别是横幅的纯视觉设计），您是否同意？或者有其他纯视觉的想法希望融入吗？"
第四阶段：迭代优化与最终确认 (包括配文的最终形态与合规的宣传素材)
1.接收反馈并修改：AI仔细理解用户的修改意见，特别是关于每个表情是否有配文、配文内容的具体调整，以及宣传素材的视觉构思。
2.再次呈现修改后方案：清晰展示修改后的内容，并再次请用户确认。
3.(循环1-2步，直至用户对所有内容完全满意)
第五阶段：生成专业需求文档 (严格按用户最终确认的配文指令及宣传素材规范)
1.最终确认：
    *   "太好了！我们已经就所有细节（包括每个表情的配文和宣传素材设计）达成一致了。我现在就将这些最终确认好的内容，为您整理成一份给设计师/Lovart的专业制作需求文档。"
2.生成需求文档：
    *   严格按照以下Markdown结构，将所有最终确认的信息填入。
	---
        # 微信表情包制作需求：[表情包名称]
你是一个专业的表情包设计师/插画师，负责根据以下需求设计并制作一套名为“[表情包名称]”的卡通形象表情包。
重要指令：所有图像素材（包括单个表情图片、横幅、封面、图标）均需使用 GPT Image 工具进行生成。特别注意：如下方表情描述中包含文字，所有文字内容必须严格使用指定的简体中文。所有宣传素材（尤其是横幅）必须严格遵守微信平台规范，特别是关于文字使用的限制。
        ## 任务目标
        创作一套共[数量]个符合微信表情开放平台规范的[动态/静态]表情包图片（包含用户最终确认的配文或无配文状态），以及严格符合规范的配套宣传素材。
        ## 项目背景与主角设定
        *   主角名称：[主角名称]
        *   核心性格：[核心性格描述]
        *   整体风格定位：[风格描述]
        *   灵感来源/参考图处理方式：[用户提供的参考及处理方式]
        ## 输入要求：角色卡通形象塑造规范
        [详细角色形象规范]
        *   所有表情图片均需按 1:1 (正方形) 宽高比创作 (对应微信平台240x240像素要求)。
        ## 判断规则与设计要点 (通用)
        [通用设计规范]
        *   配文规范（如适用）：若下方具体表情描述中包含【配文】，则该配文为用户最终确认的内容，必须严格使用简体中文，字体风格需与整体[Q萌/卡通等用户确认的]风格统一。
        ## 具体表情内容需求 (共[数量]个)
        请严格按照以下描述及指定的配文（如有）进行创作：
        [此处逐条列出每个表情的详细描述：
        【表情主题/名称】【画面描述】【配文】：[用户最终确认的单段简体中文配文内容，或明确注明“无配文”]
        ]
        ## 宣传素材需求 (符合微信平台规范)
        1.  表情包横幅 (Detail Page Banner):
            *   宽高比 (Aspect Ratio)：15:8 (对应微信平台750x400像素要求)
            *   内容描述：[用户确认的横幅纯视觉内容和风格要求]
            *   关键规范：
                *   严格禁止在横幅图片中出现任何文字信息 (包括但不限于推广信息、版权信息、表情包名称、标点符号、特殊符号、阿拉伯数字等)。
                *   避免使用纯白色或纯透明背景 (若主体是浅色系，背景尽量选择深色系做区分)。
                *   图片不能出现圆角、不能是九宫格样式。
                *   图像内容需与表情包主题相关，画面丰富，有故事性。
        2.  表情包封面 (Emoticon Cover):
            *   宽高比 (Aspect Ratio)：1:1 (正方形) (对应微信平台240x240像素要求)
            *   内容描述：[用户确认的封面内容和风格要求]
            *   规范提醒：[微信平台对封面的关键规范，如PNG格式、背景透明、图片清晰、避免纯头部等]
        3.  表情包图标 (Chat Page Icon):
            *   宽高比 (Aspect Ratio)：1:1 (正方形) (对应微信平台50x50像素要求)
            *   内容描述：[用户确认的图标内容和风格要求]
            *   规范提醒：[微信平台对图标的关键规范，如PNG格式、背景透明、图片清晰、通常使用头部区域等]
        请确认以上需求，如有疑问请及时沟通。期待您的精彩创作！
		4.  交付：
		    *   "这是最终的表情包制作需求文档，您可以直接使用它来对接后续的创作了！"
    ---
核心交互原则：
*   AI主动建议配文，用户决定有无及内容：AI在初步方案中为每个表情提供建议的简体中文配文，用户核心决策是保留、修改还是移除配文。
*   聚焦配文决策：在反馈环节，将“配文有无及内容确认”作为核心步骤。
*   指令的最终性和强制性：一旦用户确认，最终需求文档中的配文指令（包括语言）具有高度强制性。
*   严格遵守平台规范：在引导和生成需求时，主动强调并执行微信平台关于宣传素材（尤其是横幅无文字）的关键规范。
*   方案中心化，用户轻松批阅：AI主动承担前期创意工作，用户聚焦于对完整方案的反馈和决策。
*   专业性与灵活性兼顾：AI提供的方案应体现专业性，同时也能灵活响应用户的个性化需求。
*   保持耐心与积极：在迭代修改过程中，始终保持耐心和积极的态度。
你现在已经准备好开始与第一位用户进行对话了。请从“第一阶段：快速需求捕获”的第一个问题开始。
```

- [ ] **锁定主体特征：** 直接把你的**定妆照**发给豆包，说：“这是我的表情包主角，请总结它的外貌特征，并以此为基础为我创作后续的表情包。”
- [ ] **确定表情包主题：** 明确你的表情包使用场景，例如**职场沟通**、**情侣日常**或**朋友闲聊**。
- [ ] **搜集表情内容：** 整理一份你最常用的表情清单，例如：**收到👌、点赞👍、啊这🤔、大佬带我飞✈️、无语😑、抓狂😡、摸摸头🤗、谢谢老板🧧**。
- [ ] **最终需求确认：** 将 **【主体特征】+【主题】+【表情内容清单】** 整合起来，形成最终的、完整的指令。请仔细检查，这是批量作画前的最后一步。
	![需求确认](http://lsky.xinqi.life:2052/up/2025/06/02/683d59ff16e1a.png)

### 阶段三：批量生成 ⚙️
- [ ] **工具A：使用豆包生成：**
	- **操作：** 在豆包对话框中粘贴整理好的需求。它会一次性生成所有图片。
	- **微调：** 可使用**重新生图**或**智能编辑**功能对不满意的单张图片进行修改。
	- **优点：** 快速、免费、操作简单。
	- **缺点：** 图片带水印，风格可能较单一。
	  ![豆包批量出图](http://lsky.xinqi.life:2052/up/2025/06/02/683d5ab108d8e.png)
- [ ] **工具B： 星流 (Lovart国内版)生成：**
	- **操作：** 登录星流官网，立即创作，右上角进入**星流 Agent**，输入需求（也可上传**定妆照**作为参考）。
	- **优点：** 作为绘画垂类智能体，出图质量和艺术感通常更高，图生图一致性更好；图片**无水印**且**支持批量下载**。
	- **缺点：** 收费。新用户有免费试用次数3次，之后需要付费（每日登录会赠送少量点数）。我测了下一套表情包大概要1000点数，也就是25块钱左右,还是蛮贵的，不过每天登录会送100多点。
	> https://www.xingliu.art?inviteCode=eZ4PhFL
### 阶段四：后期加工处理 ✨
> [!WARNING] 重要提示
> AI 生成的图片需要经过简单处理才能上传，否则可能因尺寸或文件大小问题上传失败。

- **当前方案**：
	直接访问 https://bqb.xinqi.life ，对图片进行批量去水印裁剪及分辨率转换。

- **传统方案**：

- [ ] **第一步：水印处理 (针对豆包图片)**
	- **推荐方案：** 使用手机里的**相册自带编辑功能**或**美图秀秀**等 App，选择**裁剪**功能，手动框选并裁掉图片底部的水印区域。这是最简单直接的方法。

> [!BONUS] 高手选学：用魔法打败魔法
> **注意：** 这一部分**完全是可选内容**，跳过它完全不影响你制作表情包！如果你对编程有兴趣，可以尝试让 cursor/Augment/Gemini/Kimi K2帮你写一段 Python 脚本，来自动完成裁剪。这会让你对 AI 的代码能力有一个非常直观的认识。对于绝大多数同学，我们**强烈推荐使用前面的手动裁剪方法**。

- [ ] **第二步：图片压缩 (关键步骤！)**
	- **原因：** 微信表情开放平台对上传的图片文件大小有严格限制。大部分AI产品出图的像素要比微信表情包的要求高。
	- **操作指南：** 打开手机或电脑浏览器，进入图片批量压缩站点，对图片进行压缩


### 阶段五：发布上线 🎉
- [ ] **登录微信表情开放平台**[https://sticker.weixin.qq.com/]，按照指引填写信息、上传图片即可。
- [ ] **AI 辅助：** 如果你不想自己构思表情包的**名称和简介**，别忘了，可以让 AI 帮你生成几个有趣的方案。
![image.png](http://lsky.xinqi.life:2052/up/2025/06/02/683d5d181ae91.png)

---

> [!SUCCESS] 课程总结
> 恭喜你，成功完成了第一套 AI 表情包的创作！通过这次实践，你不仅收获了一套独一无二的作品，更重要的是，你亲身体验了从**多模态模型理解**、**提示词设计**、**图像模型创作**到**自动化工作流**的全过程。你已经掌握了与 AI 高效协作的思维和方法。这只是一个开始，快去把你的表情包分享给朋友们吧！


---

## 附录

### AI 绘画界的武林高手们

> [!NOTE] 温馨提示
> 了解这些业界最顶尖的工具，能帮你更全面地理解 AI 绘画的能力边界和不同工具的性格。你可以把它们想象成 AI 艺术界的“几位宗师”，各有各的独门绝技。

> 视频图片领域，推荐关注公众号 阿真lrene 
> https://mp.weixin.qq.com/s/R0Z2_M_5hWkVgJeBLzbUdA



### 🎨 三位宗师：Midjourney, Stable Diffusion, DALL·E 3

#### **Midjourney：追求极致美学的艺术家**

*   **出品方**：一个同名的独立研究实验室 Midjourney, Inc.
*   **特点**：
    *   **天生的艺术家**：Midjourney 以其出色的**艺术感和美学表现**而闻名。就算你只给一个很简单的词，它也总能生成一张构图、光影、色彩都非常惊艳的图片，自带一种电影大片般的质感。
    *   **操作友好**：它主要通过聊天软件 `Discord` 来使用，你只需要输入 `/imagine` 指令加上你的想法就行，对新手非常友好，不需要学习复杂的参数。
    *   **风格鲜明**：它的优点是出图好看，但反过来说，它也有自己一套很强的“默认风格”，有时你想跳出这个框架会需要一些技巧。
*   **适合谁**：追求高级感、希望快速得到充满艺术气息作品的用户。它就像一位品味极佳的艺术家，你只要提出想法，他就能帮你完美呈现，但会带上自己强烈的个人风格。

#### **Stable Diffusion：自由度最高的“魔法工具箱”**

*   **出品方**：由 Stability AI 公司支持的开源项目。**“开源”** 是它最重要的关键词。
*   **特点**：
    *   **极致的自由与控制**：因为是开源的，全世界的开发者都可以研究它、改造它。这让 Stable Diffusion 拥有了海量的**定制化模型**（比如专门画二次元的、画写实照片的）和强大的**控制插件**（比如可以完美复刻人物姿势的 ControlNet）。
    *   **高可玩性**：你可以把它下载到自己性能足够好的电脑上，完全免费、不限量地使用。它就像一个巨大的乐高积木箱，只要你愿意钻研，就能拼出任何你想要的东西。
    *   **学习曲线陡峭**：强大的自由度也带来了复杂性。想要用好它，你需要了解模型的选择、参数的调整、插件的用法等等，对纯新手来说门槛最高。
*   **适合谁**：喜欢折腾、追求个性化、希望对画面有像素级控制的深度玩家和技术爱好者。

#### **ComfyUI：像搭积木一样搭建你的 AI 绘画“流水线”**

*   **它是什么**：严格来说，ComfyUI 并不是一个绘画模型，而是一个用来**控制 Stable Diffusion 模型**的**图形化界面 (UI)**。
*   **特点**：
    *   **基于“节点”的可视化流程**：这是它的核心。你不再是面对一个简单的输入框，而是在一个画布上，通过拖拽和连接不同的“功能小方块”（即**节点**）来搭建整个绘画流程。每个节点负责一个具体任务，比如“加载主模型”、“输入正面提示词”、“设置图片尺寸”、“进行采样”、“保存图片”等。
    *   **完全透明，让你看清“黑箱”**：通过这个流程图，你能清晰地看到 AI 从接收指令到生成图片的每一步，非常适合想要深入理解 AI 绘画原理的人。
    *   **极致的自由度和可扩展性**：你可以自由地组合这些节点，创造出极其复杂和个性化的工作流，比如同时使用多个风格模型（LoRA）、精确控制人物姿态（ControlNet）等等，这是普通界面难以做到的。
*   **适合谁**：不满足于简单出图，渴望**深入理解 AI 绘画原理**并实现**专业级、可复现、高度定制化创作流程**的**进阶玩家和技术控**。

#### **DALL·E 3：最会“阅读理解”的聪明学生**

*   **出品方**：大名鼎鼎的 `OpenAI`，也就是 `ChatGPT` 的创造者。
*   **特点**：
    *   **超强的指令理解能力**：这是它的核心优势。因为它和 ChatGPT 大脑相通，所以它能非常精准地理解你那些复杂、甚至是有点啰嗦的句子。你告诉它“一只戴着墨镜的猫在月球上弹吉他，背景是地球”，它就能丝毫不差地画出来，逻辑关系非常清晰。
    *   **擅长画文字**：如果你想在图片里加上准确的英文字母或单词，DALL·E 3 是目前做得最好的模型之一。
    *   **对话式生成**：它通常集成在 `ChatGPT` 或微软的 `Copilot` 里，你可以像聊天一样和它沟通，不断修改你的想法，体验非常自然。
*   **适合谁**：需要精确实现创意、对画面元素和逻辑关系有严格要求的用户。它就像一个特别聪明、听话的学生，你把要求说得越清楚，它的“作业”就完成得越好。

*   **F.1 KONTEXT**:
    *   **出品方**：由前 `Stability AI` 核心成员创立的 `Black Forest Labs` 公司研发，可以说是名门之后。
    *   **核心特性 (Kontext 的魔法)**：
        *   这个模型最神奇的地方在于，它不仅能“听懂”你的文字，更能“看懂”你给的图片，能理解**文字+图片**的上下文。
        *   **这有什么用？** 这个特性对于制作**系列表情包**简直是神技！你可以先用一张“定妆照”告诉它主角长什么样，然后它就能在后续所有表情中都保持这个角色的样貌，确保风格高度统一。角色一致性做的很好。


### 🌟 星流智能体上的其他模型

这些是在国内可以轻松体验到的优秀模型，它们通常经过了本土化的优化，更符合国内用户的使用习惯。

*   **星流 Star-3**:
    *   **出品方**：国内知名的 AI 模型社区 `LiblibAI`。也是lovart.ai创业者的前一个AI站点。
    *   **特点**：可以看作是国产模型里的佼佼者，画出来的图片在色彩、细节和整体美感上都非常出色，综合实力很强。

*   **SEEDREAM 3.0**:
    *   **出品方**：大家熟悉的 `豆包`（字节跳动）团队。
    *   **特点**：这款模型非常擅长处理中英文，甚至能在图片里画出清晰的文字。而且它生成的图片分辨率很高，画面质感和细节都处理得很好。

*   **人像摄影F.1 / 动漫游戏XL / 插画设计XL / 3D立体XL**:
    *   **特点**：这是一套“风格全家桶”。你想画二次元动漫、艺术感插画，或是像皮克斯那样的 3D 卡通，直接选用对应的模型就行。它们帮你省去了大量用来描述画风的复杂提示词，对新手非常友好。




## 课后小问卷
https://l0c34idk7v.feishu.cn/share/base/form/shrcn2SHgxIs1HIsiWvutcXELRc
![AI表情包制作分享反馈.png](http://lsky.xinqi.life:2052/up/2025/07/23/6880ec2cb6b74.png)



## 关于我

一个刚开始搭的知识库，里面有放我的微信公众号和小红书账号，感兴趣的可以了解一下。没加我微信的伙伴也欢迎加我微信交流。
https://l0c34idk7v.feishu.cn/wiki/ZnyTwQi05idJInk8HypcVF1jnlb?from=from_copylink

![489e31f79c180bc1108c870a41f9556b.jpg](http://lsky.xinqi.life:2052/up/2025/07/23/6880fb0593f81.jpg)
