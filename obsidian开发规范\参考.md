基于这次实践经验和Obsidian官方规范，我整理了一份完整的Obsidian插件开发规范文档：

# Obsidian插件开发规范

## 📋 目录
1. [项目结构规范](#项目结构规范)
2. [代码规范](#代码规范)
3. [构建配置规范](#构建配置规范)
4. [错误处理规范](#错误处理规范)
5. [设置管理规范](#设置管理规范)
6. [UI开发规范](#ui开发规范)
7. [性能优化规范](#性能优化规范)
8. [发布规范](#发布规范)

## 📁 项目结构规范

### 标准目录结构
```
my-obsidian-plugin/
├── main.ts                 # 主插件文件（必须在根目录）
├── main.js                 # 构建产物
├── manifest.json           # 插件清单（必需）
├── package.json            # 项目配置
├── esbuild.config.mjs      # 构建配置
├── version-bump.mjs        # 版本管理脚本
├── versions.json           # 版本历史
├── tsconfig.json           # TypeScript配置
├── styles.css              # 样式文件（可选）
├── README.md               # 说明文档
└── src/                    # 源代码目录（可选）
    ├── types.ts            # 类型定义
    ├── constants.ts        # 常量定义
    ├── settings.ts         # 设置管理
    └── utils.ts            # 工具函数
```

### 关键文件要求

#### manifest.json（必需）
```json
{
  "id": "my-plugin",
  "name": "My Plugin",
  "version": "1.0.0",
  "minAppVersion": "0.15.0",
  "description": "Plugin description",
  "author": "Author Name",
  "authorUrl": "https://github.com/author",
  "isDesktopOnly": false
}
```

#### versions.json（必需）
```json
{
  "1.0.0": "0.15.0"
}
```

## 💻 代码规范

### 插件主类结构
```typescript
import { Plugin, Notice, TFile, Menu, Editor, MarkdownView } from 'obsidian';

export default class MyPlugin extends Plugin {
  settings: MyPluginSettings;

  async onload(): Promise<void> {
    // 1. 加载设置
    await this.loadSettings();
    
    // 2. 初始化服务
    this.initializeServices();
    
    // 3. 注册命令和菜单
    this.registerCommands();
    this.registerMenus();
    
    // 4. 添加设置页面
    this.addSettingTab(new MySettingTab(this.app, this));
  }

  onunload(): void {
    // 清理资源
  }

  async loadSettings(): Promise<void> {
    const loadedData = await this.loadData();
    this.settings = Object.assign({}, DEFAULT_SETTINGS, loadedData);
  }

  async saveSettings(): Promise<void> {
    await this.saveData(this.settings);
  }
}
```

### 命名规范
- **插件类名**: 使用PascalCase，如 `MyPlugin`
- **文件名**: 使用kebab-case，如 `my-plugin.ts`
- **变量名**: 使用camelCase，如 `myVariable`
- **常量名**: 使用UPPER_SNAKE_CASE，如 `DEFAULT_SETTINGS`
- **接口名**: 使用PascalCase，如 `MyPluginSettings`

### TypeScript类型规范
```typescript
// 设置接口
interface MyPluginSettings {
  setting1: string;
  setting2: boolean;
  setting3: number;
}

// 默认设置
const DEFAULT_SETTINGS: MyPluginSettings = {
  setting1: 'default',
  setting2: true,
  setting3: 0
};

// 方法返回类型
async myMethod(): Promise<boolean> {
  // 实现
}

// 事件处理器
private handleEvent(params: any): Promise<void> {
  // 实现
}
```

### 代码组织原则
1. **KISS原则**: 保持简单，避免过度复杂
2. **单一职责**: 每个方法只做一件事
3. **方法长度**: 单个方法不超过50行
4. **文件长度**: 单个文件不超过1000行
5. **提取复杂逻辑**: 将复杂UI或业务逻辑提取到独立方法

## 🔧 构建配置规范

### package.json标准配置
```json
{
  "name": "obsidian-my-plugin",
  "version": "1.0.0",
  "description": "Plugin description",
  "main": "main.js",
  "scripts": {
    "dev": "node esbuild.config.mjs",
    "build": "tsc -noEmit -skipLibCheck && node esbuild.config.mjs production",
    "version": "node version-bump.mjs && git add manifest.json versions.json"
  },
  "keywords": [],
  "author": "",
  "license": "MIT",
  "devDependencies": {
    "@types/node": "^16.11.6",
    "@typescript-eslint/eslint-plugin": "5.29.0",
    "@typescript-eslint/parser": "5.29.0",
    "builtin-modules": "3.3.0",
    "esbuild": "0.17.3",
    "obsidian": "latest",
    "tslib": "2.4.0",
    "typescript": "4.7.4"
  }
}
```

### esbuild.config.mjs标准配置
```javascript
import esbuild from "esbuild";
import process from "process";
import builtins from "builtin-modules";

const banner = `/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/`;

const prod = (process.argv[2] === "production");

const context = await esbuild.context({
  banner: {
    js: banner,
  },
  entryPoints: ["main.ts"],
  bundle: true,
  external: [
    "obsidian",
    "electron",
    "@codemirror/autocomplete",
    "@codemirror/collab",
    "@codemirror/commands",
    "@codemirror/language",
    "@codemirror/lint",
    "@codemirror/search",
    "@codemirror/state",
    "@codemirror/view",
    "@lezer/common",
    "@lezer/highlight",
    "@lezer/lr",
    ...builtins
  ],
  format: "cjs",
  target: "es2018",
  logLevel: "info",
  sourcemap: prod ? false : "inline",
  treeShaking: true,
  outfile: "main.js",
  minify: prod,
});

if (prod) {
  await context.rebuild();
  process.exit(0);
} else {
  await context.watch();
}
```

### version-bump.mjs标准配置
```javascript
import { readFileSync, writeFileSync } from "fs";

const targetVersion = process.env.npm_package_version;

// read minAppVersion from manifest.json and bump version to target version
let manifest = JSON.parse(readFileSync("manifest.json", "utf8"));
const { minAppVersion } = manifest;
manifest.version = targetVersion;
writeFileSync("manifest.json", JSON.stringify(manifest, null, "\t"));

// update versions.json with target version and minAppVersion from manifest.json
let versions = JSON.parse(readFileSync("versions.json", "utf8"));
versions[targetVersion] = minAppVersion;
writeFileSync("versions.json", JSON.stringify(versions, null, "\t"));
```

## ⚠️ 错误处理规范

### 统一错误处理模式
```typescript
export default class MyPlugin extends Plugin {
  /**
   * 统一的错误处理方法
   */
  private handleError(error: Error, context: string, userMessage?: string): void {
    console.error(`[${this.manifest.name}] ${context}:`, error);
    
    const message = userMessage || `❌ ${context}失败: ${error.message}`;
    new Notice(message);
  }

  /**
   * 统一的日志记录方法
   */
  private log(message: string, level: 'info' | 'warn' | 'error' = 'info'): void {
    const prefix = `[${this.manifest.name}]`;
    switch (level) {
      case 'error':
        console.error(`${prefix} ${message}`);
        break;
      case 'warn':
        console.warn(`${prefix} ${message}`);
        break;
      default:
        console.log(`${prefix} ${message}`);
    }
  }

  /**
   * 示例方法：带错误处理的异步操作
   */
  async performAction(): Promise<void> {
    this.log('Starting action');
    
    try {
      // 执行操作
      await this.doSomething();
      this.log('Action completed successfully');
    } catch (error) {
      this.handleError(error as Error, '执行操作');
    }
  }
}
```

### 错误处理最佳实践
1. **永远不要空catch**: 至少要记录错误日志
2. **用户友好的错误消息**: 避免技术术语，提供解决建议
3. **错误分类**: 区分用户错误、系统错误和网络错误
4. **错误恢复**: 在可能的情况下提供错误恢复机制

## ⚙️ 设置管理规范

### 设置页面标准结构
```typescript
import { App, PluginSettingTab, Setting } from 'obsidian';
import MyPlugin from './main';

export class MySettingTab extends PluginSettingTab {
  plugin: MyPlugin;

  constructor(app: App, plugin: MyPlugin) {
    super(app, plugin);
    this.plugin = plugin;
  }

  display(): void {
    const { containerEl } = this;
    containerEl.empty();

    // 标题
    containerEl.createEl('h2', { text: '插件设置' });

    // 设置项
    new Setting(containerEl)
      .setName('设置名称')
      .setDesc('设置描述')
      .addText(text => text
        .setPlaceholder('请输入...')
        .setValue(this.plugin.settings.setting1)
        .onChange(async (value) => {
          this.plugin.settings.setting1 = value;
          await this.plugin.saveSettings();
        }));

    // 布尔设置
    new Setting(containerEl)
      .setName('开关设置')
      .setDesc('开关描述')
      .addToggle(toggle => toggle
        .setValue(this.plugin.settings.setting2)
        .onChange(async (value) => {
          this.plugin.settings.setting2 = value;
          await this.plugin.saveSettings();
        }));
  }
}
```

### 设置管理最佳实践
1. **即时保存**: 设置更改后立即保存
2. **输入验证**: 验证用户输入的有效性
3. **默认值**: 为所有设置提供合理的默认值
4. **分组组织**: 使用标题和描述组织相关设置

## 🎨 UI开发规范

### Notice使用规范
```typescript
// 成功消息
new Notice('✅ 操作成功！');

// 错误消息
new Notice('❌ 操作失败：具体原因');

// 警告消息
new Notice('⚠️ 注意：重要提醒');

// 带超时的消息
new Notice('消息内容', 5000); // 5秒后消失

// 持续显示的消息（需要手动隐藏）
const notice = new Notice('处理中...', 0);
// ... 执行操作
notice.hide();
```

### 模态框开发规范
```typescript
import { Modal, App } from 'obsidian';

export class MyModal extends Modal {
  constructor(app: App) {
    super(app);
  }

  onOpen() {
    const { contentEl } = this;
    contentEl.createEl('h2', { text: '模态框标题' });
    
    // 添加内容
    const content = contentEl.createDiv();
    content.setText('模态框内容');
    
    // 添加按钮
    const buttonContainer = contentEl.createDiv();
    buttonContainer.addClass('modal-button-container');
    
    const confirmBtn = buttonContainer.createEl('button', {
      text: '确认',
      cls: 'mod-cta'
    });
    confirmBtn.onclick = () => {
      this.close();
    };
  }

  onClose() {
    const { contentEl } = this;
    contentEl.empty();
  }
}
```

### 菜单开发规范
```typescript
// 文件菜单
this.registerEvent(
  this.app.workspace.on('file-menu', (menu: Menu, file: TFile) => {
    if (file instanceof TFile && file.extension === 'md') {
      menu.addItem((item) => {
        item
          .setTitle('📤 我的操作')
          .setIcon('share')
          .onClick(() => {
            this.performAction(file);
          });
      });
    }
  })
);

// 编辑器菜单
this.registerEvent(
  this.app.workspace.on('editor-menu', (menu: Menu, editor: Editor, view: MarkdownView) => {
    menu.addItem((item) => {
      item
        .setTitle('📝 编辑器操作')
        .setIcon('edit')
        .onClick(() => {
          this.performEditorAction(editor, view);
        });
    });
  })
);
```

## 🚀 性能优化规范

### 异步操作规范
```typescript
// 正确：使用async/await
async performAsyncOperation(): Promise<void> {
  try {
    const result = await this.apiCall();
    this.processResult(result);
  } catch (error) {
    this.handleError(error as Error, 'API调用');
  }
}

// 避免：阻塞主线程的同步操作
// 错误示例：
// performSyncOperation(): void {
//   const result = this.blockingOperation(); // 阻塞主线程
// }
```

### 内存管理规范
```typescript
export default class MyPlugin extends Plugin {
  private eventListeners: Array<() => void> = [];

  async onload(): Promise<void> {
    // 注册事件时保存清理函数
    const cleanup = this.registerSomeEvent();
    this.eventListeners.push(cleanup);
  }

  onunload(): void {
    // 清理所有事件监听器
    this.eventListeners.forEach(cleanup => cleanup());
    this.eventListeners = [];
    
    // 清理其他资源
    this.cleanupResources();
  }
}
```

### 数据处理规范
```typescript
// 大数据量处理：使用分批处理
async processBatchData(items: any[]): Promise<void> {
  const batchSize = 100;
  
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    await this.processBatch(batch);
    
    // 让出控制权，避免阻塞UI
    await new Promise(resolve => setTimeout(resolve, 0));
  }
}
```

## 📦 发布规范

### 版本管理
```bash
# 更新版本
npm version patch  # 1.0.0 -> 1.0.1
npm version minor  # 1.0.0 -> 1.1.0
npm version major  # 1.0.0 -> 2.0.0

# 构建发布版本
npm run build
```

### 发布检查清单
- [ ] 所有TypeScript编译无错误
- [ ] 构建成功生成main.js
- [ ] manifest.json版本号正确
- [ ] versions.json已更新
- [ ] README.md文档完整
- [ ] 功能测试通过
- [ ] 性能测试通过

### Git提交规范
```bash
# 功能提交
git commit -m "feat: 添加新功能"

# 修复提交
git commit -m "fix: 修复bug"

# 文档提交
git commit -m "docs: 更新文档"

# 重构提交
git commit -m "refactor: 重构代码"

# 发布提交
git commit -m "release: v1.0.0"
```

## 🔍 调试和测试

### 开发环境设置
```bash
# 开发模式（热重载）
npm run dev

# 生产构建
npm run build

# 类型检查
npx tsc --noEmit --skipLibCheck
```

### 调试技巧
1. **使用开发者工具**: F12打开控制台查看日志
2. **添加调试日志**: 在关键位置添加console.log
3. **断点调试**: 在TypeScript源码中设置断点
4. **错误边界**: 在可能出错的地方添加try-catch

## � 安全与审查规范

### GitHub官方审查要求
基于实际审查经验，以下是Obsidian插件提交到官方仓库时必须遵循的安全规范：

#### 1. 避免使用innerHTML/outerHTML
**问题**: 使用innerHTML存在XSS安全风险
**解决方案**: 使用安全的DOM API
```typescript
// ❌ 错误做法
element.innerHTML = `<p><strong>标题:</strong>${userInput}</p>`;

// ✅ 正确做法
const p = element.createEl('p');
const strong = p.createEl('strong');
strong.textContent = '标题:';
p.appendText(userInput);
```

#### 2. 避免通过JavaScript设置样式
**问题**: 内联样式违反最佳实践，难以维护
**解决方案**:
- 优先使用Obsidian内置CSS类
- 必要时使用CSS文件定义样式
- 避免style.cssText和style属性

```typescript
// ❌ 错误做法
element.style.cssText = `
    display: flex;
    gap: 8px;
    margin-top: 8px;
`;

// ✅ 正确做法 - 使用Obsidian内置类
element.addClass('mod-cta');

// ✅ 或者在CSS文件中定义
element.addClass('my-plugin-button');
```

#### 3. 减少console日志污染
**问题**: 过多的console调用污染开发者控制台
**解决方案**: 创建调试工具类

```typescript
// 创建Debug工具类
export class Debug {
    private static enabled = false; // 生产环境设为false

    static log(...args: any[]) {
        if (this.enabled) {
            console.log('[PluginName]', ...args);
        }
    }

    static warn(...args: any[]) {
        if (this.enabled) {
            console.warn('[PluginName]', ...args);
        }
    }

    static error(...args: any[]) {
        if (this.enabled) {
            console.error('[PluginName]', ...args);
        }
    }
}

// 使用方式
Debug.log('调试信息'); // 生产环境不输出
```

#### 4. 输入验证和净化
**问题**: 未验证的用户输入可能导致安全问题
**解决方案**: 严格验证所有外部输入

```typescript
// 验证和净化用户输入
function sanitizeInput(input: string): string {
    return input
        .trim()
        .replace(/[<>]/g, '') // 移除潜在的HTML标签
        .substring(0, 1000); // 限制长度
}

// 验证URL
function isValidUrl(url: string): boolean {
    try {
        const parsed = new URL(url);
        return ['http:', 'https:'].includes(parsed.protocol);
    } catch {
        return false;
    }
}
```

#### 5. 敏感信息处理
**问题**: 敏感信息泄露
**解决方案**:
- 不在日志中记录密码、API密钥等敏感信息
- 使用适当的加密存储敏感数据
- 避免在错误消息中暴露内部信息

```typescript
// ❌ 错误做法
console.log('API Key:', apiKey);
console.error('Database connection failed:', connectionString);

// ✅ 正确做法
Debug.log('API authentication successful');
Debug.error('Database connection failed: [connection details hidden]');
```

### 常见审查问题及解决方案

#### 问题1: "Avoid using innerHTML/outerHTML"
```typescript
// 修复前
statusDesc.innerHTML = `
    <span style="color: var(--text-success);">✅ 已授权</span><br>
    <strong>用户：</strong>${userInfo.name}
`;

// 修复后
const statusSpan = statusDesc.createEl('span', { text: '✅ 已授权' });
statusSpan.style.color = 'var(--text-success)';
statusDesc.createEl('br');
const userLabel = statusDesc.createEl('strong');
userLabel.textContent = '用户：';
statusDesc.appendText(userInfo.name);
```

#### 问题2: "Avoid setting styles through JavaScript"
```typescript
// 修复前
buttonContainer.style.cssText = `
    display: flex;
    gap: 8px;
    margin-top: 8px;
`;

// 修复后 - 方案1: 使用Obsidian内置类
buttonContainer.addClass('setting-item-control');

// 修复后 - 方案2: 必要时保留关键样式
buttonContainer.style.display = 'flex';
buttonContainer.style.gap = '8px';
```

#### 问题3: "Too many console.log calls"
```typescript
// 修复前
console.log('Processing file:', fileName);
console.error('Upload failed:', error);

// 修复后
Debug.log('Processing file:', fileName);
Debug.error('Upload failed:', error);
```

### 审查通过检查清单
提交插件前请确认：

**安全性检查**
- [ ] 没有使用innerHTML/outerHTML
- [ ] 没有通过JavaScript设置内联样式（或仅保留必要的）
- [ ] 实现了Debug日志系统，生产环境默认禁用
- [ ] 所有用户输入都经过验证和净化
- [ ] 没有在日志中记录敏感信息
- [ ] 错误处理完善，不暴露内部信息

**代码质量检查**
- [ ] 代码通过TypeScript编译，无警告
- [ ] 没有未使用的变量和导入
- [ ] 函数和类有适当的JSDoc注释
- [ ] 遵循一致的命名规范

**功能性检查**
- [ ] 功能测试完整，边界情况处理妥当
- [ ] 插件可以正常加载和卸载
- [ ] 设置页面工作正常
- [ ] 命令和菜单项正确注册

**文档检查**
- [ ] README.md包含完整的使用说明
- [ ] manifest.json信息准确
- [ ] 版本号符合语义化版本规范

## �📚 参考资源

- [Obsidian插件API文档](https://docs.obsidian.md/)
- [Obsidian示例插件](https://github.com/obsidianmd/obsidian-sample-plugin)
- [TypeScript官方文档](https://www.typescriptlang.org/docs/)
- [ESBuild文档](https://esbuild.github.io/)

---

### 审查时间线参考
- **初次提交**: 通常需要1-2周审查时间
- **修复反馈**: 24-48小时内回复修复
- **最终批准**: 修复完成后1-3天内批准

### 提高审查通过率的技巧
1. **提交前自测**: 使用检查清单逐项验证
2. **参考成功案例**: 查看已通过审查的类似插件
3. **保持代码简洁**: 避免过度复杂的实现
4. **及时响应反馈**: 快速修复审查员指出的问题
5. **详细的提交说明**: 在PR中清楚说明插件功能和特点

---

## 📝 总结

这份规范基于实际开发经验和GitHub官方审查反馈总结，涵盖了从基础开发到安全审查的完整流程。遵循这些规范可以：

✅ **提高审查通过率**: 避免常见的安全和代码质量问题
✅ **加快开发速度**: 标准化的开发流程和工具链
✅ **保证代码质量**: 统一的编码标准和最佳实践
✅ **增强用户体验**: 稳定、安全、易用的插件功能

在后续插件开发中，严格按照这些规范执行，可以大大提高开发效率和代码质量，确保插件能够顺利通过官方审查并为用户提供优质体验。
