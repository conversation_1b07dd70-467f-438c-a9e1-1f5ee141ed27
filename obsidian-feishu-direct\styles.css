/* 飞书分享插件样式 */

/* 设置页面样式 */
.feishu-setting-description {
    margin-bottom: 16px;
    color: var(--text-muted);
}

.feishu-auth-status {
    padding: 8px 12px;
    border-radius: 4px;
    margin: 8px 0;
}

.feishu-auth-status.success {
    background-color: var(--background-modifier-success);
    color: var(--text-success);
}

.feishu-auth-status.error {
    background-color: var(--background-modifier-error);
    color: var(--text-error);
}

/* 按钮样式 */
.feishu-auth-button {
    margin-top: 8px;
}

/* 文件夹选择器样式 */
.feishu-folder-item {
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    margin: 2px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.feishu-folder-item:hover {
    background-color: var(--background-modifier-hover);
}

.feishu-folder-item.selected {
    background-color: var(--background-modifier-success);
    color: var(--text-on-accent);
}

.feishu-folder-icon {
    width: 16px;
    height: 16px;
    opacity: 0.7;
}

.feishu-folder-name {
    flex: 1;
}

/* 面包屑导航样式 */
.feishu-breadcrumb {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    padding: 8px 0;
    border-bottom: 1px solid var(--background-modifier-border);
}

.feishu-breadcrumb-item {
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-accent);
}

.feishu-breadcrumb-item:hover {
    background-color: var(--background-modifier-hover);
}

.feishu-breadcrumb-item.current {
    font-weight: bold;
    color: var(--text-normal);
    cursor: default;
}

.feishu-breadcrumb-separator {
    margin: 0 8px;
    color: var(--text-muted);
}

/* 加载和错误状态 */
.feishu-loading {
    text-align: center;
    padding: 20px;
    color: var(--text-muted);
    font-style: italic;
}

.feishu-error {
    text-align: center;
    padding: 20px;
    color: var(--text-error);
    background-color: var(--background-modifier-error);
    border-radius: 6px;
    margin: 10px 0;
}

.feishu-empty {
    text-align: center;
    padding: 20px;
    color: var(--text-muted);
    font-style: italic;
}

/* 新增样式 - 替换内联样式 */

/* 成功通知按钮容器 */
.feishu-notice-buttons {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

.feishu-notice-button {
    flex: 1;
}

/* 手动授权模态框样式 */
.feishu-manual-auth-description {
    margin-bottom: 20px;
}

.feishu-manual-auth-input {
    width: 100%;
    height: 80px;
}

/* 设置页面描述样式 */
.feishu-settings-description p {
    margin: 4px 0;
}

.feishu-settings-description strong {
    font-weight: 600;
}

/* 授权状态样式 */
.feishu-auth-status-success {
    color: var(--text-success);
}

.feishu-auth-status-error {
    color: var(--text-error);
}

.feishu-auth-user-info {
    margin-top: 4px;
}

.feishu-auth-user-info strong {
    font-weight: 600;
}

/* 打赏二维码样式 */
.feishu-reward-qr-container {
    text-align: center;
    margin: 16px 0;
    padding: 16px;
    background-color: var(--background-secondary);
    border-radius: 8px;
}

.feishu-reward-qr-image {
    max-width: 200px;
    max-height: 200px;
    width: auto;
    height: auto;
    border-radius: 4px;
}

.feishu-reward-hint {
    margin-top: 8px;
    font-size: 14px;
    color: var(--text-muted);
}
