1.1. 文档概述
产品/功能名称	飞书同步插件 (Feishu Sync)
文档版本	v2.0 (Final)
产品形态	一个安装在 Obsidian 内部的社区插件。
目标用户	在 Obsidian 中进行内容创作，并希望将笔记无缝、高保真地发布到飞书文档的用户。
1.2. 用户故事 (User Stories)
作为一名团队负责人，我希望能将我在 Obsidian 中撰写的项目规划和周报，一键发布到飞书共享给团队，并保持原有的格式和图表，以便团队成员能清晰阅读。
作为一名知识工作者，我希望能将我的学习笔记和资料（包含截图和PDF附件）完整地同步到飞书知识库，以便进行团队归档和后续协作。
1.3. 核心功能与用户交互
1.3.1. 触发方式
用户可以通过以下两种方式触发同步：

命令面板: 在笔记页面，通过 Ctrl/Cmd+P 打开命令面板，执行 “Feishu Sync: 将当前笔记同步到飞书” 命令。
侧边栏图标: 点击 Obsidian 左侧侧边栏新增的“飞书”图标，触发同步。
1.3.2. 实时反馈
开始: 屏幕右上角弹出提示框（Notice）：“正在同步到飞书...”。
进度: Obsidian 底部状态栏显示详细进度，如：“飞书同步: 正在生成HTML...” 或 “飞书同步: 正在上传文件...”。
成功: 弹出提示框：“同步成功！飞书文档链接已复制到剪贴板。”。
失败: 弹出提示框，并附带清晰的错误原因，如：“同步失败：YAML中缺少title字段，请检查。” 或 “同步失败：文件总体积超过100MB，无法上传。”。
1.3.3. 插件设置界面 (Plugin Settings UI)
插件必须在 Obsidian 的设置区域提供一个专属的配置页面，包含以下选项：

飞书 API 凭证 (Feishu API Credentials):

App ID: 文本输入框。
App Secret: 密码/文本输入框。
目标飞书文件夹 Token: 文本输入框，用于存放同步后的文档。
文档标题来源 (Document Title Source):

描述: 选择生成的飞书文档标题使用哪个来源。
选项 (下拉菜单):
文件名 (Filename) - (默认选项)
YAML Front Matter 的 "title" 属性
Front Matter 处理 (Front Matter Handling):

描述: 选择如何处理笔记顶部的 YAML 属性区。
选项 (下拉菜单):
移除 (Remove) - (默认选项)
保留为代码块 (Keep as Code Block)
1.4. 功能范围与转换逻辑 (Scope & Logic)
1.4.1. 文档标题确定
逻辑: 根据用户在设置中选择的“文档标题来源”。
若选择“文件名”: 直接使用当前笔记的文件名（不含 .md 后缀）作为标题。
若选择“YAML Front Matter 的 "title" 属性”: 优先读取属性区中的 title 字段。如果该字段不存在或为空，则回退 (Fallback) 使用文件名作为标题。
1.4.2. YAML Front Matter 处理
逻辑: 根据用户在设置中选择的“Front Matter 处理”选项执行。
若为“保留为代码块”: 将解析出的整个 YAML 属性区（包含---）作为一个 yaml 代码块插入到文档最开头。
若为“移除”: 在处理时完全忽略此部分内容。
1.4.3. 核心内容转换 (Markdown to Feishu)
Obsidian 元素	转换要求
标准 Markdown	标题、列表、引用、代码块、粗体、斜体、删除线、分割线、超链接等均需正确转换。
本地文件附件 ![[...]]	图片: 转换为飞书图片块。
音频(MP3) / PDF: 转换为飞书文件块。
其他文件: 全部作为通用附件，转换为飞书文件块。
忽略项: 外部图片链接 ![](http://...) 不做处理。
Callout 块 > [!TYPE] ...	转换为飞书高亮块 (Callout Block)，并根据预设的映射表设置颜色和 Emoji 图标。
文本高亮 ==text==	转换为飞书 TextRun 中带有高亮样式的文本 ("highlight": true)。
Mermaid 图表	在本地渲染为SVG图片，再作为图片块插入到文档中。
2. 技术设计文档 (TDD) - 飞书同步插件 (Feishu Sync)
2.1. 文档概述
项目名称	Feishu Sync Plugin - Implementation Guide
技术栈	TypeScript (强制，遵循 Obsidian 插件开发规范)
核心方案	自包含HTML导入法。将所有内容在本地编译成一个内联了所有资源（图片、附件）的HTML文件，然后通过飞书的HTML导入接口进行上传和转换。
2.2. 架构与核心模块
main.ts: 插件主入口，负责生命周期管理、注册命令和UI。
settings.ts: 插件设置页面UI与数据持久化。
compiler.ts: 核心编译器。负责将Obsidian笔记内容根据用户设置，编译成一个自包含的HTML字符串。
executor.ts: API执行引擎。负责处理与飞书API的通信（上传HTML，轮询结果）。
types.ts: 定义共享的TypeScript类型，如 FeishuPluginSettings。
constants.ts: 存放常量，如Callout到飞书样式的映射表。